# 资产再平衡系统

这个系统帮助你在 CEX 和链上之间自动平衡 SOL 和 IOTX 资产，以优化套利效率。

## 功能特性

- 🔄 **自动再平衡**: 根据目标配置自动调整资产分布
- 📊 **资产监控**: 实时查看各平台的资产分布情况
- 🎯 **智能决策**: 基于容差阈值决定是否需要再平衡
- 🛡️ **错误恢复**: 支持可恢复错误的自动重试
- ⏱️ **等待机制**: 智能等待转账和跨链完成

## 目标配置

```typescript
const TARGET_ALLOCATION = {
    cex: {
        sol: 2.0,      // CEX 保持 2 SOL
        iotx: 4000     // CEX 保持 4000 IOTX
    },
    onChain: {
        solOnIotex: 1.0,    // IoTeX 链上保持 1 SOL
        iotxOnSolana: 0     // 暂不支持 IOTX 在 Solana 上
    }
};
```

## 容差设置

```typescript
const TOLERANCE = {
    sol: 0.2,    // 20% 容差 (超过此比例才触发再平衡)
    iotx: 0.15   // 15% 容差
};
```

## 使用方法

### 1. 查看当前资产分布

```bash
# 仅查看，不执行操作
bun run u/coin-base-arb/rebalance.ts
```

输出示例：
```
📊 查看当前资产分布...
💰 当前资产分布:
  CEX: 1.5000 SOL, 3500.00 IOTX
  IoTeX链: 1.2000 SOL, 5000.00 IOTX
  Solana链: 0.3000 SOL

🎯 目标配置 vs 当前配置:
┌─────────────┬──────────────┬──────────────┬──────────────┐
│    资产     │   目标数量   │   当前数量   │     差异     │
├─────────────┼──────────────┼──────────────┼──────────────┤
│ CEX SOL     │       2.0000 │       1.5000 │      -0.5000 │
│ CEX IOTX    │         4000 │         3500 │         -500 │
│ IoTeX SOL   │       1.0000 │       1.2000 │      +0.2000 │
│ Solana SOL  │            - │       0.3000 │            - │
└─────────────┴──────────────┴──────────────┴──────────────┘

💰 总资产价值: ~$775.00

⚠️  需要再平衡！运行 main() 函数执行自动再平衡
```

### 2. 执行自动再平衡

```typescript
import { main } from './rebalance';

// 执行完整的再平衡操作
await main();
```

### 3. 仅检查余额

```typescript
import { checkBalances } from './rebalance';

// 仅查看当前分布，不执行操作
await checkBalances();
```

## 再平衡策略

系统会根据当前资产分布和目标配置，自动生成最优的再平衡操作序列：

### SOL 再平衡逻辑

1. **CEX SOL 过多**:
   - 提现到 Solana → 跨链到 IoTeX (如果 IoTeX 链上 SOL 不足)

2. **CEX SOL 不足**:
   - IoTeX 链上 SOL → 跨链到 Solana → 充值到 CEX
   - 或直接使用 Solana 链上的 SOL 充值到 CEX

### IOTX 再平衡逻辑

1. **CEX IOTX 过多**:
   - 提现到 IoTeX 链

2. **CEX IOTX 不足**:
   - IoTeX 链上 IOTX → 充值到 CEX

## 操作类型

- `withdraw`: 从 CEX 提现到链上
- `deposit`: 从链上充值到 CEX  
- `bridge`: 跨链转移 (Solana ↔ IoTeX)
- `swap`: DEX 交换 (在 MIMO 上)
- `transfer`: 链上转账

## 安全特性

- ✅ **容差检查**: 只有超过设定容差才执行操作
- ✅ **错误恢复**: 可恢复错误自动重试
- ✅ **操作间隔**: 避免过快执行导致问题
- ✅ **余额验证**: 操作前检查余额充足性
- ✅ **等待确认**: 等待每个操作完成再执行下一步

## 注意事项

1. **手续费**: 所有操作都会产生手续费，系统已考虑 5% 的损耗
2. **时间**: 跨链操作可能需要 10-60 分钟
3. **网络**: 确保网络连接稳定
4. **余额**: 确保各链上有足够的 gas 费用

## 配置修改

如需修改目标配置或容差，编辑 `rebalance.ts` 文件中的相应常量：

```typescript
// 修改目标配置
const TARGET_ALLOCATION = {
    cex: {
        sol: 3.0,      // 改为 3 SOL
        iotx: 5000     // 改为 5000 IOTX
    },
    // ...
};

// 修改容差
const TOLERANCE = {
    sol: 0.15,    // 改为 15% 容差
    iotx: 0.10    // 改为 10% 容差
};
```
