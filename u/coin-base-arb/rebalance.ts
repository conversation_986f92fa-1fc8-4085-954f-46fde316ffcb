//nobundling
import { ArbitrageStrategy } from "../tools/task";
import { ArbitrageDbService } from "../libs/arb-db-new";
import { CexService } from "../libs/cex";
import { IoTubeBridgeService } from "../libs/iotube";
import { MimoService } from "../libs/mimo";
import { SolanaService } from "../libs/solana";

const config = {
    binance: {
        apiKey:
            process.env.BINANCE_API_KEY ||
            "Ma577gWl6htZWNFsnRuQB9hJQHDNzboLrWuaLxHzTGE8v5fevBf9kOc4ofKKZ3e6",
        secret:
            process.env.BINANCE_SECRET ||
            "Hkcn9zY2vEVnAXbvELJzgrZmcsZ1VPxFvb8BGwhPHZHFa75QVHfhp1xHTNY1PaTl",
    },
    mimo: {
        dbUrl:
            process.env.MIMO_DB_URL ||
            "postgresql://mimo:<EMAIL>/mimo",
        rpcUrl: process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
        routerV3: "0x17C1Ae82D99379240059940093762c5e4539aba5",
        privateKey: process.env.IOTEX_PRIVATE_KEY || 'eaf3bf892b66862489338ac05a54beb902a84e6e2e90db6ad4b6ec7f07571b1a', // IoTeX 私钥
        tokens: {
            SOL: {
                address: "0xa1f3f211d9b33f2086a800842836d67f139b9a7a",
                symbol: "SOL",
                decimals: 9,
            },
            IOTX: {
                address: "IOTX", // Native token
                symbol: "IOTX",
                decimals: 18,
            },
            WIOTX: {
                address: "0xA00744882684C3e4747faEFD68D283eA44099D03",
                symbol: "WIOTX",
                decimals: 18,
            },
        },
    },
    bridge: {
        iotexRpcUrl: "https://babel-api.mainnet.iotex.io",
        solanaRpcUrl: "https://api.mainnet-beta.solana.com",
        iotexPrivateKey: 'eaf3bf892b66862489338ac05a54beb902a84e6e2e90db6ad4b6ec7f07571b1a',
        solanaPrivateKey: '2P2nX7fuTxH5mxF5V8RM69t75MKwhoUghpz9znida5tAQujYpGWvLN3uV2PGBdqJju8iFZo11DrPAaPkxP6RrEqf',
        bridgeContractAddress: "******************************************", // 实际的桥接合约地址
    },
    solana: {
        rpcUrl: process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
        privateKey: process.env.SOLANA_PRIVATE_KEY || '2P2nX7fuTxH5mxF5V8RM69t75MKwhoUghpz9znida5tAQujYpGWvLN3uV2PGBdqJju8iFZo11DrPAaPkxP6RrEqf',
    },
    arbitrage: {
        maxTradeAmount: 100, // 最大交易金额 $100 (测试用)
        walletAddresses: {
            iotex: process.env.IOTEX_WALLET_ADDRESS || "******************************************",
            solana: process.env.SOLANA_WALLET_ADDRESS || "57hfxmbXd2SC5YESxT2bL8mSG2adoV8UJpSWSSNCxJjK",
        },
    },
};

const cexService = new CexService(config.binance);
const mimoService = new MimoService(config.mimo.privateKey);
const bridgeService = new IoTubeBridgeService(config.bridge);
const solanaService = new SolanaService(config.solana);
const arbitrageService = new ArbitrageDbService(
    cexService,
    mimoService,
    bridgeService,
    solanaService,
    config.arbitrage
);

// 目标配置：定义理想的资产分布
interface TargetAllocation {
    cex: {
        sol: number;    // CEX 上目标 SOL 数量
        iotx: number;   // CEX 上目标 IOTX 数量
    };
    onChain: {
        solOnIotex: number;   // IoTeX 链上目标 SOL 数量
        iotxOnSolana: number; // Solana 链上目标 IOTX 数量 (如果支持)
    };
}

// 当前余额接口
interface CurrentBalances {
    cex: {
        sol: number;
        iotx: number;
    };
    onChain: {
        solOnIotex: number;
        iotxOnSolana: number;
        solOnSolana: number; // Solana 链上的 SOL
    };
}

// 再平衡操作接口
interface RebalanceAction {
    type: 'transfer' | 'bridge' | 'swap' | 'withdraw' | 'deposit';
    asset: 'SOL' | 'IOTX';
    amount: number;
    from: string;
    to: string;
    priority: number; // 1-5, 1最高优先级
    description: string;
}

// 目标配置
const TARGET_ALLOCATION: TargetAllocation = {
    cex: {
        sol: 2.0,      // CEX 保持 2 SOL
        iotx: 4000     // CEX 保持 4000 IOTX
    },
    onChain: {
        solOnIotex: 1.0,    // IoTeX 链上保持 1 SOL
        iotxOnSolana: 0     // 暂不支持 IOTX 在 Solana 上
    }
};

// 容差配置（超过此比例才触发再平衡）
const TOLERANCE = {
    sol: 0.2,    // 20% 容差
    iotx: 0.15   // 15% 容差
};

/**
 * 获取当前所有余额
 */
async function getCurrentBalances(): Promise<CurrentBalances> {
    console.log("📊 获取当前资产分布...");

    // CEX 余额
    const cexSol = await cexService.getSolBalance();
    const cexIotx = await cexService.getIotxBalance();

    // IoTeX 链上余额
    const iotexSol = parseFloat(await mimoService.getTokenBalanceFormatted("SOL"));
    const iotexIotx = parseFloat(await mimoService.getTokenBalanceFormatted("IOTX"));

    // Solana 链上余额
    const solanaBalances = await solanaService.getTotalSolBalance();
    const solanaSol = solanaBalances.total;

    const balances: CurrentBalances = {
        cex: {
            sol: cexSol,
            iotx: cexIotx
        },
        onChain: {
            solOnIotex: iotexSol,
            iotxOnSolana: 0, // 暂不支持
            solOnSolana: solanaSol
        }
    };

    console.log("💰 当前资产分布:");
    console.log(`  CEX: ${cexSol.toFixed(4)} SOL, ${cexIotx.toFixed(2)} IOTX`);
    console.log(`  IoTeX链: ${iotexSol.toFixed(4)} SOL, ${iotexIotx.toFixed(2)} IOTX`);
    console.log(`  Solana链: ${solanaSol.toFixed(4)} SOL`);

    return balances;
}

/**
 * 分析需要的再平衡操作
 */
function analyzeRebalanceNeeds(current: CurrentBalances, target: TargetAllocation): RebalanceAction[] {
    const actions: RebalanceAction[] = [];

    // 分析 SOL 分布
    const solDiff = {
        cex: current.cex.sol - target.cex.sol,
        iotex: current.onChain.solOnIotex - target.onChain.solOnIotex
    };

    // 分析 IOTX 分布
    const iotxDiff = {
        cex: current.cex.iotx - target.cex.iotx,
        iotex: current.onChain.iotxOnSolana - target.onChain.iotxOnSolana // 目前为0
    };

    console.log("📈 资产差异分析:");
    console.log(`  SOL - CEX: ${solDiff.cex > 0 ? '+' : ''}${solDiff.cex.toFixed(4)}, IoTeX: ${solDiff.iotex > 0 ? '+' : ''}${solDiff.iotex.toFixed(4)}`);
    console.log(`  IOTX - CEX: ${iotxDiff.cex > 0 ? '+' : ''}${iotxDiff.cex.toFixed(2)}, IoTeX: ${iotxDiff.iotex > 0 ? '+' : ''}${iotxDiff.iotex.toFixed(2)}`);

    // SOL 再平衡逻辑
    if (Math.abs(solDiff.cex) > target.cex.sol * TOLERANCE.sol) {
        if (solDiff.cex > 0) {
            // CEX SOL 过多，需要转出
            if (solDiff.iotex < 0) {
                // IoTeX 链上 SOL 不足，从 CEX 提现到 Solana 再跨链到 IoTeX
                const transferAmount = Math.min(Math.abs(solDiff.cex), Math.abs(solDiff.iotex));
                actions.push({
                    type: 'withdraw',
                    asset: 'SOL',
                    amount: transferAmount,
                    from: 'CEX',
                    to: 'Solana',
                    priority: 2,
                    description: `从 CEX 提现 ${transferAmount.toFixed(4)} SOL 到 Solana`
                });
                actions.push({
                    type: 'bridge',
                    asset: 'SOL',
                    amount: transferAmount,
                    from: 'Solana',
                    to: 'IoTeX',
                    priority: 3,
                    description: `跨链转移 ${transferAmount.toFixed(4)} SOL 从 Solana 到 IoTeX`
                });
            }
        } else {
            // CEX SOL 不足，需要转入
            if (solDiff.iotex > 0) {
                // IoTeX 链上 SOL 过多，跨链到 Solana 再充值到 CEX
                const transferAmount = Math.min(Math.abs(solDiff.cex), Math.abs(solDiff.iotex));
                actions.push({
                    type: 'bridge',
                    asset: 'SOL',
                    amount: transferAmount,
                    from: 'IoTeX',
                    to: 'Solana',
                    priority: 2,
                    description: `跨链转移 ${transferAmount.toFixed(4)} SOL 从 IoTeX 到 Solana`
                });
                actions.push({
                    type: 'deposit',
                    asset: 'SOL',
                    amount: transferAmount,
                    from: 'Solana',
                    to: 'CEX',
                    priority: 3,
                    description: `从 Solana 充值 ${transferAmount.toFixed(4)} SOL 到 CEX`
                });
            } else if (current.onChain.solOnSolana > 0.1) {
                // 使用 Solana 链上的 SOL 充值到 CEX
                const transferAmount = Math.min(Math.abs(solDiff.cex), current.onChain.solOnSolana - 0.05); // 保留 0.05 SOL 作为 gas
                if (transferAmount > 0) {
                    actions.push({
                        type: 'deposit',
                        asset: 'SOL',
                        amount: transferAmount,
                        from: 'Solana',
                        to: 'CEX',
                        priority: 1,
                        description: `从 Solana 充值 ${transferAmount.toFixed(4)} SOL 到 CEX`
                    });
                }
            }
        }
    }

    // IOTX 再平衡逻辑
    if (Math.abs(iotxDiff.cex) > target.cex.iotx * TOLERANCE.iotx) {
        if (iotxDiff.cex > 0) {
            // CEX IOTX 过多，提现到 IoTeX 链
            const transferAmount = Math.abs(iotxDiff.cex);
            actions.push({
                type: 'withdraw',
                asset: 'IOTX',
                amount: transferAmount,
                from: 'CEX',
                to: 'IoTeX',
                priority: 2,
                description: `从 CEX 提现 ${transferAmount.toFixed(2)} IOTX 到 IoTeX 链`
            });
        } else {
            // CEX IOTX 不足，从 IoTeX 链充值
            const availableIotx = current.onChain.solOnIotex; // 这里应该是 iotx 余额，但当前结构中没有单独的 iotx
            // 注意：这里需要获取 IoTeX 链上的 IOTX 余额
            const transferAmount = Math.min(Math.abs(iotxDiff.cex), availableIotx);
            if (transferAmount > 100) { // 最小转账金额
                actions.push({
                    type: 'deposit',
                    asset: 'IOTX',
                    amount: transferAmount,
                    from: 'IoTeX',
                    to: 'CEX',
                    priority: 1,
                    description: `从 IoTeX 链充值 ${transferAmount.toFixed(2)} IOTX 到 CEX`
                });
            }
        }
    }

    // 按优先级排序
    actions.sort((a, b) => a.priority - b.priority);

    return actions;
}

export async function main() {
    console.log("🔄 开始资产再平衡检查...");

    try {
        // 1. 获取当前余额
        const currentBalances = await getCurrentBalances();

        // 2. 分析再平衡需求
        const rebalanceActions = analyzeRebalanceNeeds(currentBalances, TARGET_ALLOCATION);

        if (rebalanceActions.length === 0) {
            console.log("✅ 资产分布已平衡，无需调整");
            return;
        }

        console.log(`\n📋 需要执行 ${rebalanceActions.length} 个再平衡操作:`);
        rebalanceActions.forEach((action, index) => {
            console.log(`  ${index + 1}. [优先级${action.priority}] ${action.description}`);
        });

        // 3. 执行再平衡操作
        await executeRebalanceActions(rebalanceActions);

        console.log("✅ 资产再平衡完成");

    } catch (error) {
        console.error("❌ 资产再平衡失败:", error);
        throw error;
    }
}

/**
 * 执行再平衡操作
 */
async function executeRebalanceActions(actions: RebalanceAction[]): Promise<void> {
    console.log("\n🚀 开始执行再平衡操作...");

    for (let i = 0; i < actions.length; i++) {
        const action = actions[i];
        console.log(`\n📝 执行操作 ${i + 1}/${actions.length}: ${action.description}`);

        try {
            await executeRebalanceAction(action);
            console.log(`✅ 操作 ${i + 1} 完成`);

            // 操作间隔，避免过快执行
            if (i < actions.length - 1) {
                console.log("⏳ 等待 5 秒后执行下一个操作...");
                await new Promise(resolve => setTimeout(resolve, 5000));
            }

        } catch (error) {
            console.error(`❌ 操作 ${i + 1} 失败:`, error);

            // 根据错误类型决定是否继续
            if (isRecoverableError(error)) {
                console.log("⚠️ 可恢复错误，继续执行下一个操作");
                continue;
            } else {
                console.log("🛑 严重错误，停止执行");
                throw error;
            }
        }
    }
}

/**
 * 执行单个再平衡操作
 */
async function executeRebalanceAction(action: RebalanceAction): Promise<void> {
    switch (action.type) {
        case 'withdraw':
            await executeWithdraw(action);
            break;

        case 'deposit':
            await executeDeposit(action);
            break;

        case 'bridge':
            await executeBridge(action);
            break;

        case 'swap':
            await executeSwap(action);
            break;

        case 'transfer':
            await executeTransfer(action);
            break;

        default:
            throw new Error(`未知的操作类型: ${action.type}`);
    }
}

/**
 * 执行提现操作
 */
async function executeWithdraw(action: RebalanceAction): Promise<void> {
    if (action.asset === 'SOL') {
        const solanaAddress = config.arbitrage.walletAddresses.solana;
        await cexService.withdrawSol(action.amount, solanaAddress);

        // 等待到账
        console.log("⏳ 等待 SOL 提现到账...");
        await waitForSolanaBalanceIncrease(action.amount);

    } else if (action.asset === 'IOTX') {
        const iotexAddress = config.arbitrage.walletAddresses.iotex;
        await cexService.withdrawIotx(action.amount, iotexAddress);

        // 等待到账
        console.log("⏳ 等待 IOTX 提现到账...");
        await waitForIotexBalanceIncrease(action.amount);
    }
}

/**
 * 执行充值操作
 */
async function executeDeposit(action: RebalanceAction): Promise<void> {
    if (action.asset === 'SOL') {
        // 从 Solana 充值 SOL 到 CEX
        const depositAddress = await cexService.getSolDepositAddress();
        await solanaService.transfer(depositAddress, action.amount);

        // 等待 CEX 到账
        console.log("⏳ 等待 CEX SOL 余额增加...");
        await cexService.waitForSolBalanceIncrease(action.amount);

    } else if (action.asset === 'IOTX') {
        // 从 IoTeX 链充值 IOTX 到 CEX
        const depositAddress = await cexService.getIotxDepositAddress();
        await mimoService.sendIotx(action.amount, depositAddress);

        // 等待 CEX 到账
        console.log("⏳ 等待 CEX IOTX 余额增加...");
        await cexService.waitForIotxBalanceIncrease(action.amount);
    }
}

/**
 * 执行跨链操作
 */
async function executeBridge(action: RebalanceAction): Promise<void> {
    if (action.asset === 'SOL') {
        if (action.from === 'Solana' && action.to === 'IoTeX') {
            // Solana -> IoTeX
            const iotexAddress = config.arbitrage.walletAddresses.iotex;
            const bridgeResult = await bridgeService.transferSolFromSolanaToIotex(
                action.amount,
                iotexAddress
            );

            // 等待跨链到账
            console.log("⏳ 等待跨链转账完成...");
            await waitForSolBalanceIncrease(bridgeResult.receivedAmount);

        } else if (action.from === 'IoTeX' && action.to === 'Solana') {
            // IoTeX -> Solana
            const solanaAddress = config.arbitrage.walletAddresses.solana;
            const bridgeResult = await bridgeService.transferSolFromIotexToSolana(
                action.amount,
                solanaAddress
            );

            // 等待跨链到账
            console.log("⏳ 等待跨链转账完成...");
            await waitForSolanaWsolBalanceIncrease(bridgeResult.receivedAmount);
        }
    }
}

/**
 * 执行交换操作
 */
async function executeSwap(action: RebalanceAction): Promise<void> {
    // 在 MIMO DEX 上执行代币交换
    if (action.from === 'IoTeX') {
        if (action.asset === 'SOL') {
            // 将 IOTX 换成 SOL
            await mimoService.swapIotxToSol(action.amount);
        } else if (action.asset === 'IOTX') {
            // 将 SOL 换成 IOTX
            await mimoService.swapSolToIotx(action.amount);
        }
    }
}

/**
 * 执行转账操作
 */
async function executeTransfer(action: RebalanceAction): Promise<void> {
    // 链上转账操作
    if (action.from === 'Solana') {
        await solanaService.transfer(action.to, action.amount);
    }
    // 可以添加其他链的转账逻辑
}

/**
 * 判断是否为可恢复的错误
 */
function isRecoverableError(error: any): boolean {
    const recoverableErrors = [
        'Network timeout',
        'Rate limit exceeded',
        'Temporary service unavailable',
        'Insufficient balance', // 可能是临时的余额不足
    ];

    const errorMessage = error?.message || error?.toString() || '';
    return recoverableErrors.some(msg => errorMessage.includes(msg));
}

/**
 * 等待 Solana 余额增加
 */
async function waitForSolanaBalanceIncrease(expectedAmount: number, maxWaitTime: number = 300000): Promise<void> {
    console.log(`⏳ 等待 Solana SOL 余额增加 ${expectedAmount.toFixed(4)} SOL...`);

    const initialBalance = await solanaService.getSolBalance();
    const startTime = Date.now();
    const targetBalance = initialBalance + expectedAmount * 0.95; // 考虑手续费

    while (Date.now() - startTime < maxWaitTime) {
        try {
            const currentBalance = await solanaService.getSolBalance();

            if (currentBalance >= targetBalance) {
                const actualIncrease = currentBalance - initialBalance;
                console.log(`✅ Solana SOL 余额已增加 ${actualIncrease.toFixed(4)} SOL`);
                return;
            }

            await new Promise(resolve => setTimeout(resolve, 15000)); // 15秒检查一次
        } catch (error) {
            console.error("检查 Solana SOL 余额时出错:", error);
            await new Promise(resolve => setTimeout(resolve, 15000));
        }
    }

    throw new Error(`等待 Solana SOL 到账超时，预期增加 ${expectedAmount} SOL`);
}

/**
 * 等待 Solana WSOL 余额增加
 */
async function waitForSolanaWsolBalanceIncrease(expectedAmount: number, maxWaitTime: number = 600000): Promise<void> {
    console.log(`⏳ 等待 Solana WSOL 余额增加 ${expectedAmount.toFixed(4)} SOL...`);

    const initialBalance = await solanaService.getWsolBalance();
    const startTime = Date.now();
    const targetBalance = initialBalance + expectedAmount * 0.95; // 考虑手续费

    while (Date.now() - startTime < maxWaitTime) {
        try {
            const currentBalance = await solanaService.getWsolBalance();

            if (currentBalance >= targetBalance) {
                const actualIncrease = currentBalance - initialBalance;
                console.log(`✅ Solana WSOL 余额已增加 ${actualIncrease.toFixed(4)} SOL`);
                return;
            }

            await new Promise(resolve => setTimeout(resolve, 30000)); // 30秒检查一次，跨链较慢
        } catch (error) {
            console.error("检查 Solana WSOL 余额时出错:", error);
            await new Promise(resolve => setTimeout(resolve, 30000));
        }
    }

    throw new Error(`等待 Solana WSOL 到账超时，预期增加 ${expectedAmount} SOL`);
}

/**
 * 等待 IoTeX 链上 SOL 余额增加
 */
async function waitForSolBalanceIncrease(expectedAmount: number, maxWaitTime: number = 600000): Promise<void> {
    console.log(`⏳ 等待 IoTeX 链 SOL 余额增加 ${expectedAmount.toFixed(4)} SOL...`);

    const initialBalance = parseFloat(await mimoService.getTokenBalanceFormatted("SOL"));
    const startTime = Date.now();
    const targetBalance = initialBalance + expectedAmount * 0.95; // 考虑手续费

    while (Date.now() - startTime < maxWaitTime) {
        try {
            const currentBalance = parseFloat(await mimoService.getTokenBalanceFormatted("SOL"));

            if (currentBalance >= targetBalance) {
                const actualIncrease = currentBalance - initialBalance;
                console.log(`✅ IoTeX 链 SOL 余额已增加 ${actualIncrease.toFixed(4)} SOL`);
                return;
            }

            await new Promise(resolve => setTimeout(resolve, 30000)); // 30秒检查一次
        } catch (error) {
            console.error("检查 IoTeX 链 SOL 余额时出错:", error);
            await new Promise(resolve => setTimeout(resolve, 30000));
        }
    }

    throw new Error(`等待 IoTeX 链 SOL 到账超时，预期增加 ${expectedAmount} SOL`);
}

/**
 * 等待 IoTeX 链上 IOTX 余额增加
 */
async function waitForIotexBalanceIncrease(expectedAmount: number, maxWaitTime: number = 300000): Promise<void> {
    console.log(`⏳ 等待 IoTeX 链 IOTX 余额增加 ${expectedAmount.toFixed(2)} IOTX...`);

    const initialBalance = parseFloat(await mimoService.getTokenBalanceFormatted("IOTX"));
    const startTime = Date.now();
    const targetBalance = initialBalance + expectedAmount * 0.95; // 考虑手续费

    while (Date.now() - startTime < maxWaitTime) {
        try {
            const currentBalance = parseFloat(await mimoService.getTokenBalanceFormatted("IOTX"));

            if (currentBalance >= targetBalance) {
                const actualIncrease = currentBalance - initialBalance;
                console.log(`✅ IoTeX 链 IOTX 余额已增加 ${actualIncrease.toFixed(2)} IOTX`);
                return;
            }

            await new Promise(resolve => setTimeout(resolve, 15000)); // 15秒检查一次
        } catch (error) {
            console.error("检查 IoTeX 链 IOTX 余额时出错:", error);
            await new Promise(resolve => setTimeout(resolve, 15000));
        }
    }

    throw new Error(`等待 IoTeX 链 IOTX 到账超时，预期增加 ${expectedAmount} IOTX`);
}

/**
 * 仅查看当前资产分布，不执行再平衡
 */
export async function checkBalances() {
    console.log("📊 查看当前资产分布...");

    try {
        const currentBalances = await getCurrentBalances();
        const target = TARGET_ALLOCATION;

        console.log("\n🎯 目标配置 vs 当前配置:");
        console.log("┌─────────────┬──────────────┬──────────────┬──────────────┐");
        console.log("│    资产     │   目标数量   │   当前数量   │     差异     │");
        console.log("├─────────────┼──────────────┼──────────────┼──────────────┤");

        // CEX SOL
        const cexSolDiff = currentBalances.cex.sol - target.cex.sol;
        console.log(`│ CEX SOL     │ ${target.cex.sol.toFixed(4).padStart(12)} │ ${currentBalances.cex.sol.toFixed(4).padStart(12)} │ ${(cexSolDiff > 0 ? '+' : '') + cexSolDiff.toFixed(4).padStart(11)} │`);

        // CEX IOTX
        const cexIotxDiff = currentBalances.cex.iotx - target.cex.iotx;
        console.log(`│ CEX IOTX    │ ${target.cex.iotx.toFixed(0).padStart(12)} │ ${currentBalances.cex.iotx.toFixed(0).padStart(12)} │ ${(cexIotxDiff > 0 ? '+' : '') + cexIotxDiff.toFixed(0).padStart(11)} │`);

        // IoTeX SOL
        const iotexSolDiff = currentBalances.onChain.solOnIotex - target.onChain.solOnIotex;
        console.log(`│ IoTeX SOL   │ ${target.onChain.solOnIotex.toFixed(4).padStart(12)} │ ${currentBalances.onChain.solOnIotex.toFixed(4).padStart(12)} │ ${(iotexSolDiff > 0 ? '+' : '') + iotexSolDiff.toFixed(4).padStart(11)} │`);

        // Solana SOL (额外信息)
        console.log(`│ Solana SOL  │ ${'-'.padStart(12)} │ ${currentBalances.onChain.solOnSolana.toFixed(4).padStart(12)} │ ${'-'.padStart(12)} │`);

        console.log("└─────────────┴──────────────┴──────────────┴──────────────┘");

        // 计算总价值 (假设 SOL = $200, IOTX = $0.05)
        const solPrice = 200; // 可以从 API 获取实时价格
        const iotxPrice = 0.05;

        const totalValue =
            (currentBalances.cex.sol + currentBalances.onChain.solOnIotex + currentBalances.onChain.solOnSolana) * solPrice +
            currentBalances.cex.iotx * iotxPrice;

        console.log(`\n💰 总资产价值: ~$${totalValue.toFixed(2)}`);

        // 检查是否需要再平衡
        const needsRebalance =
            Math.abs(cexSolDiff) > target.cex.sol * TOLERANCE.sol ||
            Math.abs(cexIotxDiff) > target.cex.iotx * TOLERANCE.iotx ||
            Math.abs(iotexSolDiff) > target.onChain.solOnIotex * TOLERANCE.sol;

        if (needsRebalance) {
            console.log("\n⚠️  需要再平衡！运行 main() 函数执行自动再平衡");
        } else {
            console.log("\n✅ 资产分布良好，无需再平衡");
        }

    } catch (error) {
        console.error("❌ 获取资产分布失败:", error);
    }
}

// 如果直接运行此文件，执行检查余额功能
if (require.main === module) {
    checkBalances().catch(console.error);
}

