// nobundling
import postgres from "postgres";
import axios from 'axios';
import {main as run} from './dbrun.ts';

const mimo = postgres(
    `postgresql://mimo:<EMAIL>/mimo`,
);

const WSOL = "0xa1f3f211d9b33f2086a800842836d67f139b9a7a";
const WIOTX = "0xa00744882684c3e4747faefd68d283ea44099d03";

export async function mimoRadio() {
    const res = await mimo`select price from token_list_v3 where id = ${WSOL}`;
    const solPrice = res[0]?.price;

    const iotxRes =
        await mimo`select price from token_list_v3 where id = ${WIOTX}`;
    const iotxPrice = iotxRes[0]?.price;

    const radio = Math.floor(solPrice / iotxPrice);
    return radio;
}

export async function binanceRadio() {
    const [solRes, iotxRes] = await Promise.all([
        axios.get('https://api.binance.com/api/v3/ticker/price?symbol=SOLUSDT'),
        axios.get('https://api.binance.com/api/v3/ticker/price?symbol=IOTXUSDT')
    ]);
    
    const solPrice = parseFloat(solRes.data.price);
    const iotxPrice = parseFloat(iotxRes.data.price);
    
    const ratio = Math.floor(solPrice / iotxPrice);
    return ratio;
}

export async function getDiff() {
    const a =  await mimoRadio();
    const b = await binanceRadio();
    const diff = a - b;
    return diff;
}

export async function main() {
    const diff = await getDiff();
    if (diff > 100) {
        await axios.get(`https://api.day.app/nnwQnBZ4wcp5Gd8zveakZB/SOL-PATH-${diff}`)
    } else if (diff < -100) {
        await axios.get(`https://api.day.app/nnwQnBZ4wcp5Gd8zveakZB/IOTX-PATH-${diff}`)
    }

    if (Math.abs(diff) < 100) {
        return;
    }

    const path = diff >= 100 ? 'sol' : 'iotex';
    await run(path === 'sol' ? 500 : 1000, path);
}
