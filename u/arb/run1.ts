import { ArbitrageService } from "../libs/arbitrage";
import { CexService } from "../libs/cex";
import { IoTubeBridgeService } from "../libs/iotube";
import { MimoService } from "../libs/mimo";
import { SolanaService } from "../libs/solana";

const config = {
  binance: {
    apiKey:
      process.env.BINANCE_API_KEY ||
      "Ma577gWl6htZWNFsnRuQB9hJQHDNzboLrWuaLxHzTGE8v5fevBf9kOc4ofKKZ3e6",
    secret:
      process.env.BINANCE_SECRET ||
      "Hkcn9zY2vEVnAXbvELJzgrZmcsZ1VPxFvb8BGwhPHZHFa75QVHfhp1xHTNY1PaTl",
  },
  mimo: {
    dbUrl:
      process.env.MIMO_DB_URL ||
      "postgresql://mimo:<EMAIL>/mimo",
    rpcUrl: process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
    routerV3: "0x17C1Ae82D99379240059940093762c5e4539aba5",
    privateKey: process.env.IOTEX_PRIVATE_KEY || 'eaf3bf892b66862489338ac05a54beb902a84e6e2e90db6ad4b6ec7f07571b1a', // IoTeX 私钥
    tokens: {
      SOL: {
        address: "0xa1f3f211d9b33f2086a800842836d67f139b9a7a",
        symbol: "SOL",
        decimals: 9,
      },
      IOTX: {
        address: "IOTX", // Native token
        symbol: "IOTX",
        decimals: 18,
      },
      WIOTX: {
        address: "0xA00744882684C3e4747faEFD68D283eA44099D03",
        symbol: "WIOTX",
        decimals: 18,
      },
    },
  },
  bridge: {
    iotexRpcUrl:
      process.env.IOTEX_RPC_URL || "https://babel-api.mainnet.iotex.io",
    solanaRpcUrl:
      process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
    iotexPrivateKey: process.env.IOTEX_PRIVATE_KEY || 'eaf3bf892b66862489338ac05a54beb902a84e6e2e90db6ad4b6ec7f07571b1a',
    solanaPrivateKey: process.env.SOLANA_PRIVATE_KEY || '2P2nX7fuTxH5mxF5V8RM69t75MKwhoUghpz9znida5tAQujYpGWvLN3uV2PGBdqJju8iFZo11DrPAaPkxP6RrEqf',
    bridgeContractAddress: "******************************************", // 实际的桥接合约地址
  },
  solana: {
    rpcUrl: process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com",
    privateKey: process.env.SOLANA_PRIVATE_KEY || '2P2nX7fuTxH5mxF5V8RM69t75MKwhoUghpz9znida5tAQujYpGWvLN3uV2PGBdqJju8iFZo11DrPAaPkxP6RrEqf',
  },
  arbitrage: {
    maxTradeAmount: 100, // 最大交易金额 $100 (测试用)
    walletAddresses: {
      iotex: process.env.IOTEX_WALLET_ADDRESS || "******************************************",
      solana: process.env.SOLANA_WALLET_ADDRESS || "57hfxmbXd2SC5YESxT2bL8mSG2adoV8UJpSWSSNCxJjK",
    },
  },
};

const cexService = new CexService(config.binance);
const mimoService = new MimoService(config.mimo.privateKey);
const bridgeService = new IoTubeBridgeService(config.bridge);
const solanaService = new SolanaService(config.solana);
const arbitrageService = new ArbitrageService(
  cexService,
  mimoService,
  bridgeService,
  solanaService,
  config.arbitrage
);

export async function main(diff: number) {
    if (Math.abs(diff) < 50) {
        console.log('差值小于 50，跳过');
        return;
    }

    // diff > 0: MIMO上SOL换IOTX更划算，执行买SOL路径
    if (diff > 50) {
        console.log('MIMO上SOL换IOTX更划算，执行：币安买SOL→跨链→MIMO换IOTX→提现卖IOTX');
        await arbitrageService.execSolPath(20);
    } else if (diff < -50) {
        console.log('币安上SOL换IOTX更划算，执行：币安买IOTX→跨链→MIMO换SOL→提现卖SOL');
        await arbitrageService.execIotexPath(20);
    }

    console.log('done');
}
