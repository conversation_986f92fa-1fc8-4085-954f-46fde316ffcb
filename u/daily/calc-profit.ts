import { AccountAdapter } from "../tools/account.ts";

export async function main() {
  
  const account = new AccountAdapter();
  const result = await account.sql`select task_id from task where profit = '0' limit 1`;
  const task_id = result[0]?.task_id;
  if (!task_id) {
    console.log('没有需要计算的任务');
    return;
  }

  const profitRes = await account.sql`with latest_task as (
  select task_id
  from task
  where status = 'completed'
  order by created_at desc
  limit 1
),
orders as (
  select
    step_name,
    (execution_result -> 'buyOrder' ->> 'cost')::numeric as buy_cost,
    (execution_result -> 'sellOrder' ->> 'cost')::numeric as sell_cost
  from task_detail
  where task_id = (${task_id})
    and step_name in ('buy_token', 'sell_token')
)
select
  (select sell_cost from orders where step_name = 'sell_token') -
  (select buy_cost from orders where step_name = 'buy_token') as profit`

  const profit = profitRes[0]?.profit;
  await account.sql`update task set profit = ${profit}::varchar where task_id = ${task_id}`;
  console.log('done')
}
