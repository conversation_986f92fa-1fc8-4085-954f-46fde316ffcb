import { CexService } from "../libs/cex";
import { AccountAdapter } from "../tools/account.ts";

export async function main() {
  const cex = new CexService({
    apiKey:
      process.env.BINANCE_API_KEY ||
      "Ma577gWl6htZWNFsnRuQB9hJQHDNzboLrWuaLxHzTGE8v5fevBf9kOc4ofKKZ3e6",
    secret:
      process.env.BINANCE_SECRET ||
      "Hkcn9zY2vEVnAXbvELJzgrZmcsZ1VPxFvb8BGwhPHZHFa75QVHfhp1xHTNY1PaTl",
  });

  const usdtBalance = await cex.getUsdtBalance();
  const account = new AccountAdapter();
  await account.create({
    data: {
      date_time: new Date().toISOString(),
      amount: usdtBalance,
    },
  });

  console.log('done')
}
